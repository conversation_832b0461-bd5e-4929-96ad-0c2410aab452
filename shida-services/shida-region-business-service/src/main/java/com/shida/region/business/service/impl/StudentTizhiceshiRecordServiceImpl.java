package com.shida.region.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shida.AccountCommonService;
import com.shida.enums.RestCode;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.excel.ExcelHelper;
import com.shida.exceptions.ServiceException;
import com.shida.pojo.vo.AccountInfo;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.StudentTizhiceshiRecord;
import com.shida.region.business.mapper.StudentTizhiceshiRecordMapper;
import com.shida.region.business.pojo.excel.StudentTiZhiCeShiExportExcel;
import com.shida.region.business.pojo.excel.StudentTiZhiCeShiImportExcel;
import com.shida.region.business.pojo.search.StudentTiZhiCeShiSearch;
import com.shida.region.business.pojo.vo.StudentTiZhiCeShiInfo;
import com.shida.region.business.service.IStudentTizhiceshiRecordService;
import com.shida.region.business.tenant.TenantContextHolder;
import com.shida.region.business.entity.Clazz;
import com.shida.region.business.entity.Student;
import com.shida.region.business.mapper.ClazzMapper;
import com.shida.region.business.mapper.StudentMapper;
import com.shida.region.business.pojo.search.StudentSearch;
import com.shida.region.business.pojo.vo.StudentInfo;
import com.shida.region.business.service.IClazzService;
import com.shida.region.business.service.IStudentService;
import com.shida.entity.BaseEntity;
import cn.hutool.core.bean.BeanUtil;
import com.shida.userCenter.api.IFeignSportProjectService;
import com.shida.userCenter.api.IFeignTermService;
import com.shida.userCenter.dto.OrgDto;
import com.shida.userCenter.dto.SportProjectDto;
import com.shida.userCenter.dto.TermDto;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 体质健康标准测试结果记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05
 */
@Slf4j
@Service
public class StudentTizhiceshiRecordServiceImpl extends ServiceImpl<StudentTizhiceshiRecordMapper, StudentTizhiceshiRecord> implements IStudentTizhiceshiRecordService {

    @Resource
    private IFeignTermService feignTermService;

    @Resource
    private IFeignSportProjectService feignSportProjectService;

    @Resource
    private TenantContextHolder tenantContextHolder;

    @Resource
    private StudentMapper studentMapper;

    @Resource
    private ClazzMapper clazzMapper;

    @Override
    public PageInfo<StudentTiZhiCeShiInfo> getPageData(StudentTiZhiCeShiSearch search) {
        IPage<StudentTiZhiCeShiInfo> page = new Page<>(search.getPageNumber(), search.getPageSize());
        page = baseMapper.getPageData(page, search);

        // 获取租户信息
        OrgDto orgDto = tenantContextHolder.getTenant();

        // 获取学期信息
        TermDto termDto = feignTermService.getById(search.getTermId());

        // 获取体育项目列表
        List<SportProjectDto> sportProjects = feignSportProjectService.getFitnessTestProjects();

        // 处理每条记录
        for (StudentTiZhiCeShiInfo record : page.getRecords()) {
            // 设置区域和学期信息
            record.setRegionName(orgDto.getName());
            if (termDto != null) {
                record.setTermName(termDto.getTermName());
            }



            // 初始化项目测试结果
            initializeProjectResults(record, sportProjects);

            // 计算总分和等级
            calculateTotalScoreAndGrade(record);
        }

        return PageInfo.fromMybatisPage(page);
    }


    /**
     * 初始化项目测试结果
     */
    private void initializeProjectResults(StudentTiZhiCeShiInfo record, List<SportProjectDto> sportProjects) {
        Map<String, StudentTiZhiCeShiInfo.ProjectTestResult> projectResults = new HashMap<>();

        // 查询学生的体育项目测试结果
        List<StudentTizhiceshiRecord> sportResults = super.lambdaQuery()
                .eq(StudentTizhiceshiRecord::getStudentId, record.getStudentId())
                .eq(StudentTizhiceshiRecord::getTermId, record.getTermId())
                .isNotNull(StudentTizhiceshiRecord::getKmid)
                .list();

        // 构建项目ID到测试结果的映射
        Map<Long, StudentTizhiceshiRecord> sportResultMap = sportResults.stream()
                .collect(Collectors.toMap(StudentTizhiceshiRecord::getKmid, result -> result));

        for (SportProjectDto project : sportProjects) {
            if (!"BMI".equals(project.getCode())) { // BMI已经单独处理
                StudentTiZhiCeShiInfo.ProjectTestResult result = new StudentTiZhiCeShiInfo.ProjectTestResult();
                result.setProjectCode(project.getCode());
                result.setProjectName(project.getName());

                // 从数据库查询的结果中获取实际数据
                StudentTizhiceshiRecord sportResult = sportResultMap.get(project.getId());
                if (sportResult != null) {
                    result.setResult(sportResult.getKscj() != null ? sportResult.getKscj().toString() : "--");
                    result.setScore(sportResult.getFs() != null ? sportResult.getFs() : BigDecimal.ZERO);
                    result.setGrade("--"); // 等级需要根据分数计算
                } else {
                    result.setResult("--");
                    result.setScore(BigDecimal.ZERO);
                    result.setGrade("--");
                }

                projectResults.put(project.getCode(), result);
            }
        }

        record.setProjectResults(projectResults);
    }

    /**
     * 计算总分和等级
     */
    private void calculateTotalScoreAndGrade(StudentTiZhiCeShiInfo record) {
        BigDecimal totalScore = BigDecimal.ZERO;
        // 加上其他项目分数
        if (record.getProjectResults() != null) {
            for (StudentTiZhiCeShiInfo.ProjectTestResult result : record.getProjectResults().values()) {
                if (result.getScore() != null) {
                    totalScore = totalScore.add(result.getScore());
                }
            }
        }

        record.setTotalScore(totalScore);

        // 根据总分计算等级
        if (totalScore.compareTo(new BigDecimal("90")) >= 0) {
            record.setGrade("优秀");
        } else if (totalScore.compareTo(new BigDecimal("80")) >= 0) {
            record.setGrade("良好");
        } else if (totalScore.compareTo(new BigDecimal("60")) >= 0) {
            record.setGrade("及格");
        } else {
            record.setGrade("不及格");
        }
    }

    @Override
    public void downloadBatchImportTemplate(HttpServletResponse response, String regionId, Long termId) {
        TermDto termDto = feignTermService.getById(termId);
        if (termDto == null) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "学期信息不存在");
        }

        // 从数据库查询体育项目
        List<SportProjectDto> sportProjects = feignSportProjectService.getFitnessTestProjects();

        ExcelWriter excelWriter = ExcelUtil.getBigWriter();

        // 设置基础表头
        List<String> headers = new ArrayList<>();
        headers.add("学生姓名");
        headers.add("学籍号");
        headers.add("性别");
        headers.add("班级名称");
        headers.add("是否残疾");
        headers.add("是否伤病");
        headers.add("身高(cm)");
        headers.add("体重(kg)");

        // 动态添加体育项目表头
        for (SportProjectDto project : sportProjects) {
            headers.add(project.getName());
        }

        // 写入表头
        excelWriter.writeHeadRow(headers);

        // 导出Excel
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" +
                java.net.URLEncoder.encode(termDto.getTermName() + "体质测试导入模板.xlsx", "UTF-8"));
        } catch (Exception e) {
            response.setHeader("Content-Disposition", "attachment;filename=" + termDto.getTermName() + "体质测试导入模板.xlsx");
        }

        try {
            excelWriter.flush(response.getOutputStream(), true);
        } catch (IOException e) {
            log.error("模板下载失败", e);
            throw new ServiceException(RestCode.EXCEL_REPORT_ERROR, "模板下载失败");
        } finally {
            excelWriter.close();
        }
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public List<ExcelBatchImportResponse> batchImport(MultipartFile file, Long termId) {
        AccountInfo accountInfo = AccountCommonService.getCurrentUser();

        // 权限验证
        String schoolName = accountInfo.getSchoolName();
        if (StrUtil.isBlank(schoolName)) {
            log.error("该用户获取学校姓名异常：{}", accountInfo.getUsername());
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "导入失败，体质测试请使用学校账号导入");
        }

        // 获取合法的学期信息
        TermDto term = getValidTermById(termId);

        // 读取模板相关信息
        List<List<Object>> readResult = null;
        try {
            ExcelReader excelReader = ExcelUtil.getReader(file.getInputStream());
            readResult = excelReader.read();
        } catch (IOException exception) {
            log.error("读取模板数据发生错误，请稍后重试", exception);
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "读取模板数据发生错误");
        }
        if (CollUtil.isEmpty(readResult)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "模板数据为空，请下载最新模板");
        }

        // 校验模板标题是否符合标准
        checkExcelTemplate(readResult.get(0), term);

        // 结果集（去标题后结果集）
        List<List<Object>> resultData = readResult.subList(1, readResult.size());
        if (ObjectUtil.isEmpty(resultData)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "模板内容数据为空，请填写数据");
        }

        // 基础数据检查
        List<ExcelBatchImportResponse> errors = baseCheckExcelData(resultData, accountInfo, term);

        // 深度校验数据的准确性以及合法性（进行数据库层面的校验）
        TiZhiDeepCheckExcelDataInfo deepCheckResultInfo = deepCheckExcelData(resultData, errors, accountInfo);
        // 数据库中学生对象
        Map<String, Student> studentStutusMap = deepCheckResultInfo.getStudentStutusMap();
        // 数据库中的ID对象
        Map<Long, Student> studentIdMap = deepCheckResultInfo.getStudentIdMap();
        // 数据库中班级对象
        Map<String, Clazz> clazzMap = deepCheckResultInfo.getClazzMap();

        if (CollUtil.isNotEmpty(errors)) {
            return errors;
        }

        // 转换为BO对象让数据库存储
        List<StudentTizhiceshiRecord> tizhiceshiRecordList = convertTizhiceshiRecord(resultData, studentStutusMap, clazzMap, term, accountInfo);

        // 先删除原数据
        List<Long> studentIds = studentIdMap.values().stream().map(BaseEntity::getId).collect(Collectors.toList());
        super.lambdaUpdate().in(StudentTizhiceshiRecord::getStudentId, studentIds)
                .eq(StudentTizhiceshiRecord::getTermId, termId).remove();

        // 导入新数据
        super.saveBatch(tizhiceshiRecordList);

        return null;
    }

    @Override
    public boolean deleteData(Long studentId, Long termId) {
        return super.remove(new LambdaQueryWrapper<StudentTizhiceshiRecord>()
                .eq(StudentTizhiceshiRecord::getStudentId, studentId)
                .eq(StudentTizhiceshiRecord::getTermId, termId));
    }

    @Override
    public void exportTiZhiCeShi(StudentTiZhiCeShiSearch search, HttpServletResponse response) {
        OrgDto region = tenantContextHolder.getTenant();
        search.setPageNumber(1).setPageSize(1000);
        PageInfo<StudentTiZhiCeShiInfo> pageInfo;
        ExcelHelper<StudentTiZhiCeShiExportExcel> excelHelper = ExcelHelper.create(StudentTiZhiCeShiExportExcel.class);
        AtomicInteger index = new AtomicInteger(1);

        do {
            pageInfo = this.getPageData(search);
            List<StudentTiZhiCeShiExportExcel> excelList = pageInfo.getRecords().stream().map(info -> {
                StudentTiZhiCeShiExportExcel exportExcel = new StudentTiZhiCeShiExportExcel();
                exportExcel.setRowNum(index.getAndIncrement());
                exportExcel.setStudentName(info.getStudentName());
                exportExcel.setXjh(info.getXjh());
                exportExcel.setXb(info.getXb() != null ? (info.getXb() == 1 ? "男" : "女") : "");
                exportExcel.setClazzName(info.getClazzName());
                exportExcel.setSchoolName(info.getSchoolName());
                exportExcel.setRegionName(info.getRegionName());
                exportExcel.setSfcj(info.getSfcj() != null ? (info.getSfcj() ? "是" : "否") : "");
                exportExcel.setSfsb(info.getSfsb() != null ? (info.getSfsb() ? "是" : "否") : "");
                exportExcel.setSg(info.getSg());
                exportExcel.setTz(info.getTz());
                exportExcel.setTotalScore(info.getTotalScore());
                exportExcel.setGrade(info.getGrade());

                // 设置项目成绩和分数
                if (info.getProjectResults() != null) {
                    info.getProjectResults().forEach((code, result) -> {
                        switch (code) {
                            case "VITAL_CAPACITY":
                                exportExcel.setFhlResult(result.getResult());
                                exportExcel.setFhlScore(result.getScore());
                                break;
                            case "50M_RUN":
                                exportExcel.setWsmResult(result.getResult());
                                exportExcel.setWsmScore(result.getScore());
                                break;
                            case "SIT_AND_REACH":
                                exportExcel.setZwtqqResult(result.getResult());
                                exportExcel.setZwtqqScore(result.getScore());
                                break;
                            case "STANDING_LONG_JUMP":
                                exportExcel.setLdtyResult(result.getResult());
                                exportExcel.setLdtyScore(result.getScore());
                                break;
                            case "PULL_UP":
                            case "SIT_UP":
                                exportExcel.setPowerResult(result.getResult());
                                exportExcel.setPowerScore(result.getScore());
                                break;
                            case "800M_RUN":
                            case "1000M_RUN":
                                exportExcel.setEnduranceResult(result.getResult());
                                exportExcel.setEnduranceScore(result.getScore());
                                break;
                        }
                    });
                }

                return exportExcel;
            }).collect(java.util.stream.Collectors.toList());

            excelHelper.writeData(excelList);
            search.setPageNumber(search.getPageNumber() + 1);
        } while (pageInfo.hasNext());

        excelHelper.writeToWebResponse(response, region.getName() + "-体质测试结果");
    }

    /**
     * 获取合法的学期信息
     */
    private TermDto getValidTermById(Long termId) {
        TermDto term = feignTermService.getById(termId);
        if (ObjectUtil.isEmpty(term)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "非法学期");
        }
        if (term.getStatus().equals(3)) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "当前学期已归档，无法导入学生信息");
        }
        return term;
    }

    /**
     * 校验Excel模板标题
     */
    private void checkExcelTemplate(List<Object> headers, TermDto term) {
        // 获取体育项目列表
        List<SportProjectDto> sportProjects = feignSportProjectService.getFitnessTestProjects();

        // 基础表头
        List<String> expectedHeaders = new ArrayList<>();
        expectedHeaders.add("学生姓名");
        expectedHeaders.add("学籍号");
        expectedHeaders.add("性别");
        expectedHeaders.add("班级名称");
        expectedHeaders.add("是否残疾");
        expectedHeaders.add("是否伤病");
        expectedHeaders.add("身高(cm)");
        expectedHeaders.add("体重(kg)");

        // 动态添加体育项目表头
        for (SportProjectDto project : sportProjects) {
            expectedHeaders.add(project.getName());
        }

        // 校验表头数量
        if (headers.size() != expectedHeaders.size()) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR,
                String.format("模板表头数量不正确，期望%d列，实际%d列", expectedHeaders.size(), headers.size()));
        }

        // 校验表头内容
        for (int i = 0; i < expectedHeaders.size(); i++) {
            String expected = expectedHeaders.get(i);
            String actual = headers.get(i) != null ? headers.get(i).toString().trim() : "";
            if (!expected.equals(actual)) {
                throw new ServiceException(RestCode.ARGUMENT_ERROR,
                    String.format("第%d列表头不正确，期望'%s'，实际'%s'", i + 1, expected, actual));
            }
        }
    }

    /**
     * 基础数据检查
     */
    private List<ExcelBatchImportResponse> baseCheckExcelData(List<List<Object>> resultData,
                                                              AccountInfo accountInfo, TermDto term) {
        List<ExcelBatchImportResponse> errors = new ArrayList<>();

        for (int i = 0; i < resultData.size(); i++) {
            List<Object> row = resultData.get(i);
            int rowNum = i + 2; // Excel行号（从1开始，加上表头）

            // 检查基础字段
            String studentName = getCellValue(row, 0);
            String xjh = getCellValue(row, 1);
            String xb = getCellValue(row, 2);
            String clazzName = getCellValue(row, 3);
            String sgStr = getCellValue(row, 6);
            String tzStr = getCellValue(row, 7);

            // 学生姓名不能为空
            if (StrUtil.isBlank(studentName)) {
                errors.add(new ExcelBatchImportResponse(rowNum, "学生姓名不能为空"));
                continue;
            }

            // 学籍号不能为空
            if (StrUtil.isBlank(xjh)) {
                errors.add(new ExcelBatchImportResponse(rowNum, "学籍号不能为空"));
                continue;
            }

            // 性别不能为空且必须是男或女
            if (StrUtil.isBlank(xb) || (!"男".equals(xb) && !"女".equals(xb))) {
                errors.add(new ExcelBatchImportResponse(rowNum, "性别必须填写'男'或'女'"));
                continue;
            }

            // 班级名称不能为空
            if (StrUtil.isBlank(clazzName)) {
                errors.add(new ExcelBatchImportResponse(rowNum, "班级名称不能为空"));
                continue;
            }

            // 身高不能为空且必须是数字
            if (StrUtil.isBlank(sgStr)) {
                errors.add(new ExcelBatchImportResponse(rowNum, "身高不能为空"));
                continue;
            }
            try {
                BigDecimal sg = new BigDecimal(sgStr);
                if (sg.compareTo(BigDecimal.ZERO) <= 0 || sg.compareTo(new BigDecimal("300")) > 0) {
                    errors.add(new ExcelBatchImportResponse(rowNum, "身高必须在0-300cm之间"));
                    continue;
                }
            } catch (NumberFormatException e) {
                errors.add(new ExcelBatchImportResponse(rowNum, "身高必须是数字"));
                continue;
            }

            // 体重不能为空且必须是数字
            if (StrUtil.isBlank(tzStr)) {
                errors.add(new ExcelBatchImportResponse(rowNum, "体重不能为空"));
                continue;
            }
            try {
                BigDecimal tz = new BigDecimal(tzStr);
                if (tz.compareTo(BigDecimal.ZERO) <= 0 || tz.compareTo(new BigDecimal("200")) > 0) {
                    errors.add(new ExcelBatchImportResponse(rowNum, "体重必须在0-200kg之间"));
                    continue;
                }
            } catch (NumberFormatException e) {
                errors.add(new ExcelBatchImportResponse(rowNum, "体重必须是数字"));
                continue;
            }
        }

        return errors;
    }

    /**
     * 获取单元格值
     */
    private String getCellValue(List<Object> row, int index) {
        if (index >= row.size() || row.get(index) == null) {
            return "";
        }
        return row.get(index).toString().trim();
    }

    /**
     * 深度校验数据的准确性以及合法性（进行数据库层面的校验）
     */
    private TiZhiDeepCheckExcelDataInfo deepCheckExcelData(List<List<Object>> resultData,
                                                           List<ExcelBatchImportResponse> errors,
                                                           AccountInfo accountInfo) {
        TiZhiDeepCheckExcelDataInfo deepCheckResultInfo = new TiZhiDeepCheckExcelDataInfo();

        // 收集所有学籍号
        List<String> xjhList = resultData.stream()
                .map(row -> getCellValue(row, 1))
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        // 收集所有班级名称
        List<String> clazzNameList = resultData.stream()
                .map(row -> getCellValue(row, 3))
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        // 查询学生信息
        StudentSearch studentSearch = new StudentSearch();
        studentSearch.setSchoolId(accountInfo.getSchoolId());
        List<StudentInfo> studentInfoList = studentMapper.getStudentAllList(studentSearch, xjhList);

        // 构建学生映射
        Map<String, Student> studentStutusMap = new HashMap<>();
        Map<Long, Student> studentIdMap = new HashMap<>();
        for (StudentInfo studentInfo : studentInfoList) {
            Student student = new Student();
            BeanUtil.copyProperties(studentInfo, student);
            studentStutusMap.put(studentInfo.getXjh(), student);
            studentIdMap.put(studentInfo.getId(), student);
        }

        // 查询班级信息
        List<Clazz> clazzList = clazzMapper.selectList(new LambdaQueryWrapper<Clazz>()
                .eq(Clazz::getSchoolId, accountInfo.getSchoolId())
                .in(Clazz::getName, clazzNameList));

        // 构建班级映射
        Map<String, Clazz> clazzMap = clazzList.stream()
                .collect(Collectors.toMap(Clazz::getName, clazz -> clazz));

        // 校验数据
        for (int i = 0; i < resultData.size(); i++) {
            List<Object> row = resultData.get(i);
            int rowNum = i + 2;

            String xjh = getCellValue(row, 1);
            String clazzName = getCellValue(row, 3);

            // 校验学生是否存在
            if (!studentStutusMap.containsKey(xjh)) {
                errors.add(new ExcelBatchImportResponse(rowNum, "学籍号为'" + xjh + "'的学生不存在"));
                continue;
            }

            // 校验班级是否存在
            if (!clazzMap.containsKey(clazzName)) {
                errors.add(new ExcelBatchImportResponse(rowNum, "班级'" + clazzName + "'不存在"));
                continue;
            }

            // 校验学生是否属于该班级
            Student student = studentStutusMap.get(xjh);
            Clazz clazz = clazzMap.get(clazzName);
            if (!student.getClazzId().equals(clazz.getId())) {
                errors.add(new ExcelBatchImportResponse(rowNum, "学生'" + getCellValue(row, 0) + "'不属于班级'" + clazzName + "'"));
                continue;
            }
        }

        deepCheckResultInfo.setStudentStutusMap(studentStutusMap);
        deepCheckResultInfo.setStudentIdMap(studentIdMap);
        deepCheckResultInfo.setClazzMap(clazzMap);

        return deepCheckResultInfo;
    }

    /**
     * 转换为体质测试记录对象
     */
    private List<StudentTizhiceshiRecord> convertTizhiceshiRecord(List<List<Object>> resultData,
                                                                  Map<String, Student> studentStutusMap,
                                                                  Map<String, Clazz> clazzMap,
                                                                  TermDto term,
                                                                  AccountInfo accountInfo) {
        List<StudentTizhiceshiRecord> recordList = new ArrayList<>();

        // 获取体育项目列表
        List<SportProjectDto> sportProjects = feignSportProjectService.getFitnessTestProjects();

        for (List<Object> row : resultData) {
            String xjh = getCellValue(row, 1);
            Student student = studentStutusMap.get(xjh);
            if (student == null) {
                continue;
            }

            String clazzName = getCellValue(row, 3);
            Clazz clazz = clazzMap.get(clazzName);
            if (clazz == null) {
                continue;
            }

            // 获取基础信息
            String sfcjStr = getCellValue(row, 4);
            String sfsbStr = getCellValue(row, 5);
            String sgStr = getCellValue(row, 6);
            String tzStr = getCellValue(row, 7);

            Boolean sfcj = "是".equals(sfcjStr);
            Boolean sfsb = "是".equals(sfsbStr);
            BigDecimal sg = StrUtil.isNotBlank(sgStr) ? new BigDecimal(sgStr) : null;
            BigDecimal tz = StrUtil.isNotBlank(tzStr) ? new BigDecimal(tzStr) : null;

            // 为每个体育项目创建一条记录
            int startIndex = 8; // 体育项目从第9列开始
            for (int i = 0; i < sportProjects.size(); i++) {
                SportProjectDto project = sportProjects.get(i);
                int columnIndex = startIndex + i;

                String testResult = "";
                if (columnIndex < row.size() && row.get(columnIndex) != null) {
                    testResult = row.get(columnIndex).toString().trim();
                }

                // 只有当测试结果不为空时才创建记录
                if (StrUtil.isNotBlank(testResult) && !"--".equals(testResult)) {
                    StudentTizhiceshiRecord record = new StudentTizhiceshiRecord();
                    record.setStudentId(student.getId());
                    record.setClazzId(clazz.getId());
                    record.setSchoolId(accountInfo.getSchoolId());
                    record.setTermId(term.getId());
                    record.setSfcj(sfcj);
                    record.setSfsb(sfsb);
                    record.setSg(sg);
                    record.setTz(tz);
                    record.setKmid(project.getId()); // 科目ID就是体育项目ID

                    // 解析测试成绩
                    try {
                        record.setKscj(new BigDecimal(testResult));
                    } catch (NumberFormatException e) {
                        // 如果不是数字，可能是时间格式等，暂时存储原始值
                        record.setKscj(BigDecimal.ZERO);
                    }

                    // 分数暂时设为0，后续可以根据标准计算
                    record.setFs(BigDecimal.ZERO);

                    recordList.add(record);
                }
            }
        }

        return recordList;
    }

    /**
     * 深度校验结果信息类
     */
    private static class TiZhiDeepCheckExcelDataInfo {
        private Map<String, Student> studentStutusMap;
        private Map<Long, Student> studentIdMap;
        private Map<String, Clazz> clazzMap;

        // getters and setters
        public Map<String, Student> getStudentStutusMap() {
            return studentStutusMap;
        }

        public void setStudentStutusMap(Map<String, Student> studentStutusMap) {
            this.studentStutusMap = studentStutusMap;
        }

        public Map<Long, Student> getStudentIdMap() {
            return studentIdMap;
        }

        public void setStudentIdMap(Map<Long, Student> studentIdMap) {
            this.studentIdMap = studentIdMap;
        }

        public Map<String, Clazz> getClazzMap() {
            return clazzMap;
        }

        public void setClazzMap(Map<String, Clazz> clazzMap) {
            this.clazzMap = clazzMap;
        }
    }
}
