package com.shida.region.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shida.region.business.service.ISportProjectScoreService;
import com.shida.user.center.api.IFeignPTestStandardService;
import com.shida.user.center.entity.PTestStandard;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 体育项目评分计算服务实现类
 * 
 * <AUTHOR>
 * @since 2025-09-06
 */
@Service
@Slf4j
public class SportProjectScoreServiceImpl implements ISportProjectScoreService {

    @Resource
    private IFeignPTestStandardService feignPTestStandardService;

    @Override
    public BigDecimal calculateScore(String projectCode, BigDecimal testResult, String gradeName, Integer gender) {
        if (testResult == null || testResult.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        
        try {
            // 获取评分标准代码
            String standardCode = getStandardCode(projectCode, gender);
            
            // 查询评分标准
            List<PTestStandard> standards = feignPTestStandardService.getStandardsByCodeAndGrade(standardCode, gradeName);
            
            if (standards == null || standards.isEmpty()) {
                log.warn("未找到评分标准，项目代码：{}，年级：{}，性别：{}", projectCode, gradeName, gender);
                return BigDecimal.ZERO;
            }
            
            // 根据测试结果查找对应的分数
            for (PTestStandard standard : standards) {
                if (isInRange(testResult, standard.getMinValue(), standard.getMaxValue())) {
                    return new BigDecimal(standard.getScore());
                }
            }
            
            log.warn("测试结果超出评分标准范围，项目代码：{}，测试结果：{}，年级：{}，性别：{}", 
                    projectCode, testResult, gradeName, gender);
            return BigDecimal.ZERO;
            
        } catch (Exception e) {
            log.error("计算体育项目得分失败，项目代码：{}，测试结果：{}，年级：{}，性别：{}", 
                    projectCode, testResult, gradeName, gender, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public String calculateGrade(BigDecimal score) {
        if (score == null) {
            return "--";
        }
        
        if (score.compareTo(new BigDecimal("90")) >= 0) {
            return "优秀";
        } else if (score.compareTo(new BigDecimal("80")) >= 0) {
            return "良好";
        } else if (score.compareTo(new BigDecimal("60")) >= 0) {
            return "及格";
        } else {
            return "不及格";
        }
    }

    @Override
    public String getStandardCode(String projectCode, Integer gender) {
        // 根据性别确定前缀：1男生，2女生
        String genderPrefix = (gender != null && gender == 1) ? "1" : "2";
        
        // 根据项目代码映射到评分标准代码
        switch (projectCode) {
            case "VITAL_CAPACITY":
                return genderPrefix + "FHLDX"; // 肺活量单项
            case "50M_RUN":
                return genderPrefix + "50MPD"; // 50米跑单项
            case "SIT_AND_REACH":
                return genderPrefix + "ZWTQQ"; // 坐位体前屈单项
            case "STANDING_LONG_JUMP":
                return genderPrefix + "LDTYDX"; // 立定跳远单项
            case "PULL_UP":
                return genderPrefix + "YTXSDX"; // 引体向上单项（男生）
            case "SIT_UP":
                return genderPrefix + "YWQZDX"; // 仰卧起坐单项（女生）
            case "800M_RUN":
                return genderPrefix + "800MPD"; // 800米跑单项（女生）
            case "1000M_RUN":
                return genderPrefix + "1000MPD"; // 1000米跑单项（男生）
            default:
                log.warn("未知的项目代码：{}", projectCode);
                return genderPrefix + "UNKNOWN";
        }
    }
    
    /**
     * 判断测试结果是否在指定范围内
     * 
     * @param testResult 测试结果
     * @param minValue 最小值
     * @param maxValue 最大值
     * @return 是否在范围内
     */
    private boolean isInRange(BigDecimal testResult, BigDecimal minValue, BigDecimal maxValue) {
        if (testResult == null) {
            return false;
        }
        
        // 如果最小值为空，只判断是否小于等于最大值
        if (minValue == null && maxValue != null) {
            return testResult.compareTo(maxValue) <= 0;
        }
        
        // 如果最大值为空，只判断是否大于等于最小值
        if (minValue != null && maxValue == null) {
            return testResult.compareTo(minValue) >= 0;
        }
        
        // 如果都不为空，判断是否在范围内
        if (minValue != null && maxValue != null) {
            return testResult.compareTo(minValue) >= 0 && testResult.compareTo(maxValue) <= 0;
        }
        
        // 如果都为空，返回false
        return false;
    }
}
