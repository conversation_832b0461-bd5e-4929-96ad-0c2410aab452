package com.shida.region.business.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.shida.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 体质健康标准测试结果记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("b_student_tizhiceshi_record")
@Schema(name = "StudentTizhiceshiRecord", description = "体质健康标准测试结果记录表")
public class StudentTizhiceshiRecord extends BaseEntity<StudentTizhiceshiRecord> {

    private static final long serialVersionUID = 1L;

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "班级ID")
    private Long clazzId;

    @Schema(description = "学校ID")
    private Long schoolId;

    @Schema(description = "学期ID")
    private Long termId;

    @Schema(description = "是否伤病")
    private Boolean sfsb;

    @Schema(description = "是否残疾")
    private Boolean sfcj;

    @Schema(description = "身高(cm)")
    private BigDecimal sg;

    @Schema(description = "体重(kg)")
    private BigDecimal tz;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
