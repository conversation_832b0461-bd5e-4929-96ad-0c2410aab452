package com.shida.region.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.shida.region.business.entity.StudentTizhiceshiRecord;
import com.shida.region.business.pojo.search.StudentTiZhiCeShiSearch;
import com.shida.region.business.pojo.vo.StudentTiZhiCeShiInfo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 体质健康标准测试结果记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05
 */
public interface StudentTizhiceshiRecordMapper extends BaseMapper<StudentTizhiceshiRecord> {

    /**
     * 分页查询学生体质测试信息
     *
     * @param page   分页对象
     * @param search 搜索条件
     * @return 分页结果
     */
    IPage<StudentTiZhiCeShiInfo> getPageData(IPage<StudentTiZhiCeShiInfo> page, @Param("search") StudentTiZhiCeShiSearch search);
}
