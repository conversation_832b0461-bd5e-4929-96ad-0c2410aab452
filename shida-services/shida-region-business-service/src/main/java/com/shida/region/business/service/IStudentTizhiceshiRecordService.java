package com.shida.region.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shida.excel.ExcelBatchImportResponse;
import com.shida.pojo.vo.PageInfo;
import com.shida.region.business.entity.StudentTizhiceshiRecord;
import com.shida.region.business.pojo.search.StudentTiZhiCeShiSearch;
import com.shida.region.business.pojo.vo.StudentTiZhiCeShiInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 体质健康标准测试结果记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-5
 */
public interface IStudentTizhiceshiRecordService extends IService<StudentTizhiceshiRecord> {

    /**
     * 分页查询学生体质测试信息
     *
     * @param search 搜索条件
     * @return 分页结果
     */
    PageInfo<StudentTiZhiCeShiInfo> getPageData(StudentTiZhiCeShiSearch search);

    /**
     * 下载批量导入模板
     *
     * @param response HttpServletResponse
     * @param regionId 区域ID
     * @param termId   学期ID
     */
    void downloadBatchImportTemplate(HttpServletResponse response, String regionId, Long termId);

    /**
     * 批量导入体质测试结果
     *
     * @param file   Excel文件
     * @param termId 学期ID
     * @return 导入结果
     */
    List<ExcelBatchImportResponse> batchImport(MultipartFile file, Long termId);

    /**
     * 删除学生体质测试数据
     *
     * @param studentId 学生ID
     * @param termId    学期ID
     * @return 是否删除成功
     */
    boolean deleteData(Long studentId, Long termId);

    /**
     * 导出体质测试结果
     *
     * @param search   搜索条件
     * @param response HttpServletResponse
     */
    void exportTiZhiCeShi(StudentTiZhiCeShiSearch search, HttpServletResponse response);
}
