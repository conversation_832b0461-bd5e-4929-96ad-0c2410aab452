package com.shida.region.business.pojo.excel;

import cn.hutool.core.util.StrUtil;
import com.shida.enums.RestCode;
import com.shida.exceptions.ServiceException;
import com.shida.userCenter.dto.SportProjectDto;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学生体质测试导入Excel实体
 * 使用动态字段来处理不同的体育项目
 *
 * <AUTHOR>
 * @since 2025-09-05
 */
@Data
@Accessors(chain = true)
public class StudentTiZhiCeShiImportExcel {

    private String studentName;  // 学生姓名
    private String xjh;          // 学籍号
    private String xb;           // 性别
    private String clazzName;    // 班级名称
    private String sfcj;         // 是否残疾
    private String sfsb;         // 是否伤病
    private BigDecimal sg;       // 身高(cm)
    private BigDecimal tz;       // 体重(kg)

    // 动态体育项目成绩，key为项目代码，value为成绩
    private Map<String, String> projectResults;

    /**
     * 从Excel行数据构建导入对象
     *
     * @param items Excel行数据
     * @param sportProjects 体育项目列表
     * @return 导入对象
     */
    public static StudentTiZhiCeShiImportExcel readBuilder(List<Object> items, List<SportProjectDto> sportProjects) {
        if (items.size() < 8) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "Excel数据列数不足，请检查模板格式");
        }

        StudentTiZhiCeShiImportExcel excel = new StudentTiZhiCeShiImportExcel();

        // 基础字段
        excel.setStudentName(items.get(0) != null ? items.get(0).toString().trim() : "");
        excel.setXjh(items.get(1) != null ? items.get(1).toString().trim() : "");
        excel.setXb(items.get(2) != null ? items.get(2).toString().trim() : "");
        excel.setClazzName(items.get(3) != null ? items.get(3).toString().trim() : "");
        excel.setSfcj(items.get(4) != null ? items.get(4).toString().trim() : "");
        excel.setSfsb(items.get(5) != null ? items.get(5).toString().trim() : "");

        // 身高体重处理
        try {
            String sgStr = items.get(6) != null ? items.get(6).toString().trim() : "";
            if (StrUtil.isNotBlank(sgStr)) {
                excel.setSg(new BigDecimal(sgStr));
            }
        } catch (NumberFormatException e) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "身高格式错误，请输入数字");
        }

        try {
            String tzStr = items.get(7) != null ? items.get(7).toString().trim() : "";
            if (StrUtil.isNotBlank(tzStr)) {
                excel.setTz(new BigDecimal(tzStr));
            }
        } catch (NumberFormatException e) {
            throw new ServiceException(RestCode.ARGUMENT_ERROR, "体重格式错误，请输入数字");
        }

        // 动态体育项目成绩
        Map<String, String> projectResults = new HashMap<>();
        int startIndex = 8; // 体育项目从第9列开始

        for (int i = 0; i < sportProjects.size(); i++) {
            SportProjectDto project = sportProjects.get(i);
            int columnIndex = startIndex + i;

            if (columnIndex < items.size()) {
                String result = items.get(columnIndex) != null ? items.get(columnIndex).toString().trim() : "";
                projectResults.put(project.getCode(), result);
            } else {
                projectResults.put(project.getCode(), "");
            }
        }

        excel.setProjectResults(projectResults);
        return excel;
    }
}
