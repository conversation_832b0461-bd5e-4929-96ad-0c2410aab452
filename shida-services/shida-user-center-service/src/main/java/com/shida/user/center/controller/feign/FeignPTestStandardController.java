package com.shida.user.center.controller.feign;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shida.user.center.entity.PTestStandard;
import com.shida.user.center.service.IPTestStandardService;
import com.shida.userCenter.api.IFeignPTestStandardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 体质健康测试评分标准 Feign Controller
 * 
 * <AUTHOR>
 * @since 2025-09-06
 */
@Tag(name = "Feign-体质健康测试评分标准服务")
@RestController
@RequestMapping("/ptest-standard-feign-api")
public class FeignPTestStandardController implements IFeignPTestStandardService {

    @Resource
    private IPTestStandardService pTestStandardService;

    @Operation(summary = "根据标准代码和年级查询评分标准")
    @PostMapping("/getStandardsByCodeAndGrade")
    @Override
    public List<PTestStandard> getStandardsByCodeAndGrade(@RequestParam("code") String code, 
                                                          @RequestParam("gradeName") String gradeName) {
        return pTestStandardService.list(new LambdaQueryWrapper<PTestStandard>()
                .eq(PTestStandard::getCode, code)
                .eq(PTestStandard::getGradeName, gradeName)
                .orderByDesc(PTestStandard::getScore));
    }
}
