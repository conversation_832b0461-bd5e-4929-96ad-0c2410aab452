package com.shida.userCenter.api;

import com.shida.FeignInterceptor;
import com.shida.user.center.entity.PTestStandard;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 体质健康测试评分标准 Feign 接口
 * 
 * <AUTHOR>
 * @since 2025-09-06
 */
@FeignClient(name = "shida-user-center-service", contextId = "ptest-standard-api", configuration = FeignInterceptor.class)
public interface IFeignPTestStandardService {

    /**
     * 根据标准代码和年级查询评分标准
     * 
     * @param code 标准代码
     * @param gradeName 年级名称
     * @return 评分标准列表
     */
    @PostMapping("/ptest-standard-feign-api/getStandardsByCodeAndGrade")
    List<PTestStandard> getStandardsByCodeAndGrade(@RequestParam("code") String code, 
                                                   @RequestParam("gradeName") String gradeName);
}
