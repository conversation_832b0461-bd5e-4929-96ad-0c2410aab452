package com.shida.userCenter.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 体育项目DTO
 *
 * <AUTHOR>
 * @since 2025-09-05
 */
@Data
@Accessors(chain = true)
@Schema(name = "SportProjectDto", description = "体育项目数据传输对象")
public class SportProjectDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "项目名称")
    private String name;

    @Schema(description = "项目代码")
    private String code;

    @Schema(description = "描述")
    private String description;
}
